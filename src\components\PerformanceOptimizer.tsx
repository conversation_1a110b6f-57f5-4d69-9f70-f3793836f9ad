'use client';

import { useEffect } from 'react';
import { initializeLazyLoading } from '@/components/LazyLoadManager';
import { optimizeFontLoading, addResourceHints } from '@/components/OptimizedFonts';

export function PerformanceOptimizer() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Initialize all performance optimizations
    const initOptimizations = () => {
      // Font loading optimization
      optimizeFontLoading();

      // Resource hints
      addResourceHints();

      // Lazy loading initialization
      initializeLazyLoading();

      // Critical resource preloading
      preloadCriticalResources();

      // Performance monitoring
      if (process.env.NODE_ENV === 'development') {
        monitorPerformance();
      }
    };

    // Initialize after DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initOptimizations);
    } else {
      initOptimizations();
    }

    return () => {
      document.removeEventListener('DOMContentLoaded', initOptimizations);
    };
  }, []);

  return null;
}

// Preload critical resources
function preloadCriticalResources() {
  if (typeof window === 'undefined') return;

  // Preload critical CSS
  const criticalCSS = `
    /* Critical above-the-fold styles */
    .hero-section { font-display: swap; }
    .navigation { contain: layout style; }
    .tool-card { contain: layout; }
  `;

  const style = document.createElement('style');
  style.textContent = criticalCSS;
  style.setAttribute('data-critical', 'true');
  document.head.appendChild(style);

  // Preload critical images
  const criticalImages = [
    '/images/logo.webp',
    '/images/hero-bg.webp'
  ];

  criticalImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
}

// Performance monitoring for development
function monitorPerformance() {
  if (typeof window === 'undefined') return;

  // Monitor Core Web Vitals
  const vitalsObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      const { name, value } = entry as any;
      
      switch (name) {
        case 'FCP':
          if (value > 1800) console.warn(`Slow FCP: ${value}ms`);
          break;
        case 'LCP':
          if (value > 2500) console.warn(`Slow LCP: ${value}ms`);
          break;
        case 'FID':
          if (value > 100) console.warn(`Poor FID: ${value}ms`);
          break;
        case 'CLS':
          if (value > 0.1) console.warn(`Poor CLS: ${value}`);
          break;
      }
    }
  });

  // Observe Web Vitals
  try {
    vitalsObserver.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
  } catch (e) {
    console.warn('Performance Observer not supported');
  }

  // Monitor long tasks
  const longTaskObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.duration > 50) {
        console.warn(`Long task detected: ${entry.duration}ms`);
      }
    }
  });

  try {
    longTaskObserver.observe({ entryTypes: ['longtask'] });
  } catch (e) {
    console.warn('Long task observer not supported');
  }
}
