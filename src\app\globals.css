@tailwind base;
@tailwind components;
@tailwind utilities;

/* Critical CSS - Above the fold */
@layer critical {
  .hero-section {
    contain: layout style;
    will-change: transform;
  }

  .tool-card {
    contain: layout;
    transform: translateZ(0);
  }

  .navigation {
    contain: layout style;
  }
}



@layer base {
  :root {
    --background: 218 93% 95%;
    --foreground: 221 25% 25%;
    --card: 0 0% 100%;
    --card-foreground: 221 25% 25%;
    --popover: 0 0% 100%;
    --popover-foreground: 221 25% 25%;
    --primary: 221 44% 41%;
    --primary-foreground: 0 0% 100%;
    --secondary: 221 44% 90%;
    --secondary-foreground: 221 44% 31%;
    --muted: 221 30% 92%;
    --muted-foreground: 221 25% 55%;
    --accent: 120 60% 67%;
    --accent-foreground: 120 100% 15%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 221 20% 88%;
    --input: 221 30% 95%;
    --ring: 221 44% 41%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 221 44% 20%;
    --sidebar-foreground: 220 30% 95%;
    --sidebar-primary: 218 93% 95%;
    --sidebar-primary-foreground: 221 44% 20%;
    --sidebar-accent: 221 44% 30%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 221 44% 25%;
    --sidebar-ring: 218 93% 85%;
  }
  .dark {
    --background: 221 44% 10%;
    --foreground: 220 30% 95%;
    --card: 221 44% 15%;
    --card-foreground: 220 30% 95%;
    --popover: 221 44% 10%;
    --popover-foreground: 220 30% 95%;
    --primary: 218 93% 85%;
    --primary-foreground: 221 44% 15%;
    --secondary: 221 44% 25%;
    --secondary-foreground: 220 30% 95%;
    --muted: 221 44% 20%;
    --muted-foreground: 220 30% 75%;
    --accent: 120 60% 67%;
    --accent-foreground: 120 100% 15%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 221 44% 25%;
    --input: 221 44% 20%;
    --ring: 218 93% 85%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 221 44% 10%;
    --sidebar-foreground: 220 30% 95%;
    --sidebar-primary: 218 93% 85%;
    --sidebar-primary-foreground: 221 44% 15%;
    --sidebar-accent: 221 44% 20%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 221 44% 25%;
    --sidebar-ring: 218 93% 85%;
  }
}

@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }

  html {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Tajawal-Optimized', 'Tajawal', system-ui, -apple-system, sans-serif;
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    font-display: swap;
    contain: layout style;
  }

  #__next {
    height: 100%;
    width: 100%;
    contain: layout;
  }

  /* Performance optimizations */
  img {
    content-visibility: auto;
    contain-intrinsic-size: 300px 200px;
  }

  .lazy-load {
    content-visibility: auto;
    contain-intrinsic-size: 0 500px;
  }
}

@layer utilities {
  /* إضافة فئات مخصصة للعرض الكامل */
  .w-screen-safe {
    width: 100vw;
    max-width: 100vw;
  }

  .container-full {
    width: 100% ;
    max-width: none ;
    padding-left: 1rem ;
    padding-right: 1rem ;
    margin-left: 0 ;
    margin-right: 0 ;
  }

  @media (min-width: 640px) {
    .container-full {
      padding-left: 2rem ;
      padding-right: 2rem ;
    }
  }

  @media (min-width: 1024px) {
    .container-full {
      padding-left: 3rem ;
      padding-right: 3rem ;
    }
  }

  @media (min-width: 1280px) {
    .container-full {
      padding-left: 4rem ;
      padding-right: 4rem ;
    }
  }

  /* RTL and Arabic Language Optimizations */
  .rtl-text {
    direction: rtl;
    text-align: right;
  }

  .ltr-text {
    direction: ltr;
    text-align: left;
  }

  /* Arabic Typography Enhancements */
  .arabic-text {
    font-family: 'Tajawal-Optimized', 'Tajawal', 'Amiri', 'Noto Sans Arabic', system-ui, sans-serif;
    line-height: 1.8;
    letter-spacing: 0.02em;
    word-spacing: 0.1em;
    font-display: swap;
    text-rendering: optimizeLegibility;
    contain: layout style;
  }

  .arabic-heading {
    font-family: 'Wafeq', 'Tajawal-Optimized', 'Tajawal', 'Amiri', system-ui, serif;
    font-weight: 600;
    line-height: 1.4;
    font-display: swap;
    contain: layout style;
  }

  /* Arabic Number Formatting */
  .arabic-numbers {
    font-feature-settings: "lnum" 1;
    font-variant-numeric: lining-nums;
  }

  /* RTL-specific spacing and alignment */
  .rtl-space-x-2 > * + * {
    margin-right: 0.5rem;
    margin-left: 0;
  }

  .rtl-space-x-4 > * + * {
    margin-right: 1rem;
    margin-left: 0;
  }

  .rtl-ml-auto {
    margin-right: auto;
    margin-left: 0;
  }

  .rtl-mr-auto {
    margin-left: auto;
    margin-right: 0;
  }

  /* Arabic bullet points */
  .arabic-list {
    list-style: none;
    padding-right: 1.5rem;
    padding-left: 0;
  }

  .arabic-list li {
    position: relative;
    margin-bottom: 0.5rem;
  }

  .arabic-list li::before {
    content: "•";
    color: hsl(var(--primary));
    font-weight: bold;
    position: absolute;
    right: -1.2rem;
    top: 0;
  }

  /* Arabic form elements */
  .arabic-input {
    text-align: right;
    direction: rtl;
  }

  .arabic-input::placeholder {
    text-align: right;
    direction: rtl;
  }

  /* Arabic table styling */
  .arabic-table {
    direction: rtl;
  }

  .arabic-table th,
  .arabic-table td {
    text-align: right;
  }

  /* Arabic breadcrumb */
  .arabic-breadcrumb {
    direction: rtl;
  }

  .arabic-breadcrumb .separator::before {
    content: "‹";
    margin: 0 0.5rem;
  }

  /* Arabic pagination */
  .arabic-pagination {
    direction: rtl;
  }

  /* Responsive Arabic text */
  @media (max-width: 640px) {
    .arabic-text {
      font-size: 0.95rem;
      line-height: 1.7;
    }

    .arabic-heading {
      font-size: 1.1rem;
      line-height: 1.3;
    }
  }

  /* Print styles for Arabic content */
  @media print {
    .arabic-text {
      font-size: 12pt;
      line-height: 1.6;
      color: black;
    }

    .arabic-heading {
      font-size: 14pt;
      font-weight: bold;
      color: black;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .arabic-text {
      font-weight: 500;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .arabic-text {
      transition: none;
    }
  }

  /* Dark mode Arabic text adjustments */
  .dark .arabic-text {
    letter-spacing: 0.025em;
  }

  /* Arabic search highlighting */
  .arabic-highlight {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
    direction: rtl;
  }

  /* Arabic tooltip positioning */
  .arabic-tooltip {
    direction: rtl;
    text-align: right;
  }

  /* Arabic modal and dialog positioning */
  .arabic-modal {
    direction: rtl;
  }

  .arabic-modal .modal-header {
    text-align: right;
  }

  .arabic-modal .modal-footer {
    justify-content: flex-start;
  }

  /* Arabic dropdown menu */
  .arabic-dropdown {
    direction: rtl;
    text-align: right;
  }

  /* Arabic progress indicators */
  .arabic-progress {
    direction: rtl;
  }

  /* Arabic calendar styling */
  .arabic-calendar {
    direction: rtl;
  }

  .arabic-calendar .calendar-header {
    text-align: right;
  }

  /* Arabic notification positioning */
  .arabic-notification {
    direction: rtl;
    text-align: right;
  }

  /* Number display optimizations for Arabic layouts */
  .number-display {
    word-break: break-all;
    overflow-wrap: break-word;
    hyphens: none;
    white-space: normal;
  }

  .currency-display {
    word-break: break-all;
    overflow-wrap: break-word;
    hyphens: none;
    white-space: normal;
    direction: ltr;
    text-align: center;
  }

  /* Responsive number sizing */
  .responsive-number {
    font-size: clamp(1.5rem, 4vw, 3rem);
    line-height: 1.2;
  }

  @media (max-width: 640px) {
    .responsive-number {
      font-size: clamp(1.25rem, 6vw, 2rem);
    }
  }
}
