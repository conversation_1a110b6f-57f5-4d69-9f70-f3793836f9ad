'use client';

import { useEffect } from 'react';

// Font optimization utilities
export function OptimizedFonts() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Preload critical Arabic fonts
    const preloadFont = (url: string, format: string = 'woff2') => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = `font/${format}`;
      link.crossOrigin = 'anonymous';
      link.href = url;
      document.head.appendChild(link);
    };

    // Load optimized Arabic font subsets
    const loadOptimizedFonts = () => {
      // Create optimized CSS for Arabic fonts
      const style = document.createElement('style');
      style.textContent = `
        /* Arabic font optimization */
        @font-face {
          font-family: 'Tajawal-Optimized';
          font-style: normal;
          font-weight: 400;
          font-display: swap;
          src: url('https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLg.woff2') format('woff2');
          unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
        }
        
        @font-face {
          font-family: 'Tajawal-Optimized';
          font-style: normal;
          font-weight: 500;
          font-display: swap;
          src: url('https://fonts.gstatic.com/s/tajawal/v9/Iurf6YBj_oCad4k1rzSLDg.woff2') format('woff2');
          unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
        }
        
        @font-face {
          font-family: 'Tajawal-Optimized';
          font-style: normal;
          font-weight: 700;
          font-display: swap;
          src: url('https://fonts.gstatic.com/s/tajawal/v9/Iurf6YBj_oCad4k1r0iLDg.woff2') format('woff2');
          unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
        }

        /* Fallback fonts for better performance */
        .font-arabic-optimized {
          font-family: 'Tajawal-Optimized', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
          font-feature-settings: "liga" 1, "kern" 1, "calt" 1;
          text-rendering: optimizeLegibility;
        }

        /* Critical CSS for above-the-fold content */
        .hero-text {
          font-family: 'Tajawal-Optimized', system-ui, -apple-system, sans-serif;
          font-weight: 700;
          font-display: swap;
        }

        .body-text {
          font-family: 'Tajawal-Optimized', system-ui, -apple-system, sans-serif;
          font-weight: 400;
          font-display: swap;
        }
      `;
      document.head.appendChild(style);
    };

    // Load fonts asynchronously
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', loadOptimizedFonts);
    } else {
      loadOptimizedFonts();
    }

    return () => {
      // Cleanup if needed
    };
  }, []);

  return null;
}

// Font loading strategy
export function optimizeFontLoading() {
  if (typeof window === 'undefined') return;

  // Use Font Loading API if available
  if ('fonts' in document) {
    const fontPromises = [
      new FontFace('Tajawal-Optimized', 'url(https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLg.woff2)', {
        weight: '400',
        display: 'swap',
        unicodeRange: 'U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC'
      }),
      new FontFace('Tajawal-Optimized', 'url(https://fonts.gstatic.com/s/tajawal/v9/Iurf6YBj_oCad4k1rzSLDg.woff2)', {
        weight: '500',
        display: 'swap',
        unicodeRange: 'U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC'
      }),
      new FontFace('Tajawal-Optimized', 'url(https://fonts.gstatic.com/s/tajawal/v9/Iurf6YBj_oCad4k1r0iLDg.woff2)', {
        weight: '700',
        display: 'swap',
        unicodeRange: 'U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC'
      })
    ];

    Promise.all(fontPromises.map(font => font.load())).then(fonts => {
      fonts.forEach(font => {
        (document as any).fonts.add(font);
      });
      document.documentElement.classList.add('fonts-loaded');
    }).catch(error => {
      console.warn('Font loading failed:', error);
      // Fallback to system fonts
      document.documentElement.classList.add('fonts-fallback');
    });
  }
}

// Resource hints optimization
export function addResourceHints() {
  if (typeof window === 'undefined') return;

  const hints = [
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
    { rel: 'dns-prefetch', href: '//www.google-analytics.com' },
    { rel: 'dns-prefetch', href: '//www.googletagmanager.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossOrigin: 'anonymous' },
  ];

  hints.forEach(hint => {
    const link = document.createElement('link');
    link.rel = hint.rel;
    link.href = hint.href;
    if (hint.crossOrigin) {
      link.crossOrigin = hint.crossOrigin;
    }
    document.head.appendChild(link);
  });
}
