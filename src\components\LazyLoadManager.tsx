'use client';

import { lazy, Suspense, ComponentType } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy loading utilities for heavy components
export const LazyLoadManager = {
  // PDF tools - load only when needed
  PdfCompressor: lazy(() => import('@/components/tools/PdfCompressorTool').then(module => ({ default: module.PdfCompressorTool }))),
  PdfMerger: lazy(() => import('@/components/tools/PdfMergerTool').then(module => ({ default: module.PdfMergerTool }))),
  SplitPdf: lazy(() => import('@/components/tools/SplitPdfTool').then(module => ({ default: module.SplitPdfTool }))),
  
  // Image tools - load only when needed
  ImageCompressor: lazy(() => import('@/components/tools/ImageCompressorTool').then(module => ({ default: module.ImageCompressorTool }))),
  ImageEditor: lazy(() => import('@/components/tools/ImageEditorTool').then(module => ({ default: module.ImageEditorTool }))),
  ImageToWebp: lazy(() => import('@/components/tools/ImageToWebpTool').then(module => ({ default: module.ImageToWebpTool }))),
  PngToJpg: lazy(() => import('@/components/tools/PngToJpgTool').then(module => ({ default: module.PngToJpgTool }))),
  
  // Chart components - load only when needed
  Charts: lazy(() => import('recharts').then(module => ({ default: module }))),
  
  // QR Code tools - load only when needed
  QrCodeGenerator: lazy(() => import('@/components/tools/QrCodeGeneratorTool').then(module => ({ default: module.QrCodeGeneratorTool }))),
  QrCodeReader: lazy(() => import('@/components/tools/QrCodeReaderTool').then(module => ({ default: module.QrCodeReaderTool }))),
};

// Loading fallbacks for different component types
const LoadingFallbacks = {
  tool: (
    <div className="space-y-4 p-6">
      <Skeleton className="h-8 w-3/4" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-2/3" />
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
      <Skeleton className="h-12 w-32" />
    </div>
  ),
  
  chart: (
    <div className="space-y-4 p-6">
      <Skeleton className="h-6 w-1/2" />
      <Skeleton className="h-64 w-full" />
    </div>
  ),
  
  image: (
    <div className="space-y-4 p-6">
      <Skeleton className="h-6 w-1/3" />
      <Skeleton className="h-48 w-full" />
      <div className="flex space-x-2">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
      </div>
    </div>
  ),
  
  minimal: (
    <div className="flex items-center justify-center p-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  )
};

// HOC for lazy loading with error boundary
export function withLazyLoading<T extends object>(
  Component: ComponentType<T>,
  fallbackType: keyof typeof LoadingFallbacks = 'minimal'
) {
  return function LazyComponent(props: T) {
    return (
      <Suspense fallback={LoadingFallbacks[fallbackType]}>
        <Component {...props} />
      </Suspense>
    );
  };
}

// Dynamic import utilities
export const DynamicImports = {
  // Load PDF.js only when needed
  loadPdfJs: async () => {
    const [pdfLib, pdfjsDist] = await Promise.all([
      import('pdf-lib'),
      import('pdfjs-dist')
    ]);
    
    // Configure PDF.js worker
    pdfjsDist.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsDist.version}/pdf.worker.min.js`;
    
    return { pdfLib, pdfjsDist };
  },
  
  // Load image processing libraries only when needed
  loadImageLibs: async () => {
    const [html2canvas, jsPDF] = await Promise.all([
      import('html2canvas'),
      import('jspdf')
    ]);
    
    return { html2canvas: html2canvas.default, jsPDF: jsPDF.default };
  },
  
  // Load chart libraries only when needed
  loadChartLibs: async () => {
    const recharts = await import('recharts');
    return recharts;
  },
  
  // Load QR code libraries only when needed
  loadQRLibs: async () => {
    const html5QrCode = await import('html5-qrcode');
    return html5QrCode;
  },
  
  // Load file processing libraries only when needed
  loadFileLibs: async () => {
    const [jszip, fileSaver] = await Promise.all([
      import('jszip'),
      import('file-saver')
    ]);
    
    return { JSZip: jszip.default, saveAs: fileSaver.saveAs };
  }
};

// Preload critical libraries based on user interaction
export function preloadCriticalLibraries() {
  if (typeof window === 'undefined') return;
  
  // Preload on user interaction
  const preloadOnInteraction = () => {
    // Preload most commonly used libraries
    DynamicImports.loadImageLibs().catch(console.warn);
    DynamicImports.loadFileLibs().catch(console.warn);
  };
  
  // Preload on first user interaction
  const events = ['mousedown', 'touchstart', 'keydown'];
  const preloadOnce = () => {
    preloadOnInteraction();
    events.forEach(event => {
      document.removeEventListener(event, preloadOnce);
    });
  };
  
  events.forEach(event => {
    document.addEventListener(event, preloadOnce, { passive: true });
  });
  
  // Preload after page load with delay
  setTimeout(() => {
    if (document.readyState === 'complete') {
      preloadOnInteraction();
    }
  }, 3000);
}

// Resource monitoring
export function monitorResourceUsage() {
  if (typeof window === 'undefined' || !('performance' in window)) return;
  
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      const resource = entry as PerformanceResourceTiming;
      
      // Log large resources in development
      if (process.env.NODE_ENV === 'development') {
        if (resource.transferSize > 500000) { // 500KB
          console.warn(`Large resource detected: ${resource.name} (${Math.round(resource.transferSize / 1024)}KB)`);
        }
      }
      
      // Track loading times for optimization
      if (resource.name.includes('chunk') && resource.duration > 1000) {
        console.warn(`Slow chunk loading: ${resource.name} (${Math.round(resource.duration)}ms)`);
      }
    }
  });
  
  observer.observe({ entryTypes: ['resource'] });
}

// Initialize performance optimizations
export function initializeLazyLoading() {
  if (typeof window === 'undefined') return;
  
  preloadCriticalLibraries();
  
  if (process.env.NODE_ENV === 'development') {
    monitorResourceUsage();
  }
}
