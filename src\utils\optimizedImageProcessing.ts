// Optimized image processing utilities
export class OptimizedImageProcessor {
  private static canvasPool: HTMLCanvasElement[] = [];
  private static maxPoolSize = 3;

  // Canvas pool management for better memory usage
  static getCanvas(): HTMLCanvasElement {
    if (this.canvasPool.length > 0) {
      return this.canvasPool.pop()!;
    }
    return document.createElement('canvas');
  }

  static releaseCanvas(canvas: HTMLCanvasElement) {
    if (this.canvasPool.length < this.maxPoolSize) {
      // Clear canvas before returning to pool
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
      canvas.width = 0;
      canvas.height = 0;
      this.canvasPool.push(canvas);
    }
  }

  // Optimized image compression with Web Workers support
  static async compressImage(
    file: File,
    quality: number = 0.8,
    maxWidth?: number,
    maxHeight?: number
  ): Promise<{ blob: Blob; dataUrl: string; size: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        try {
          const canvas = this.getCanvas();
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            reject(new Error('Cannot get canvas context'));
            return;
          }

          // Calculate optimal dimensions
          let { width, height } = this.calculateOptimalDimensions(
            img.width,
            img.height,
            maxWidth,
            maxHeight
          );

          canvas.width = width;
          canvas.height = height;

          // Use better image rendering
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';

          // Draw image with optimizations
          ctx.drawImage(img, 0, 0, width, height);

          // Convert to blob with specified quality
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const reader = new FileReader();
                reader.onload = () => {
                  resolve({
                    blob,
                    dataUrl: reader.result as string,
                    size: blob.size
                  });
                  this.releaseCanvas(canvas);
                };
                reader.onerror = () => {
                  reject(new Error('Failed to read blob'));
                  this.releaseCanvas(canvas);
                };
                reader.readAsDataURL(blob);
              } else {
                reject(new Error('Failed to create blob'));
                this.releaseCanvas(canvas);
              }
            },
            file.type.includes('png') ? 'image/png' : 'image/jpeg',
            quality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  // Convert image format with optimization
  static async convertImageFormat(
    file: File,
    targetFormat: 'jpeg' | 'png' | 'webp',
    quality: number = 0.9
  ): Promise<{ blob: Blob; dataUrl: string }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        try {
          const canvas = this.getCanvas();
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            reject(new Error('Cannot get canvas context'));
            return;
          }

          canvas.width = img.width;
          canvas.height = img.height;

          // Handle transparency for JPEG conversion
          if (targetFormat === 'jpeg') {
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
          }

          ctx.drawImage(img, 0, 0);

          canvas.toBlob(
            (blob) => {
              if (blob) {
                const reader = new FileReader();
                reader.onload = () => {
                  resolve({
                    blob,
                    dataUrl: reader.result as string
                  });
                  this.releaseCanvas(canvas);
                };
                reader.readAsDataURL(blob);
              } else {
                reject(new Error('Failed to create blob'));
                this.releaseCanvas(canvas);
              }
            },
            `image/${targetFormat}`,
            quality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  // Calculate optimal dimensions while maintaining aspect ratio
  private static calculateOptimalDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth?: number,
    maxHeight?: number
  ): { width: number; height: number } {
    let width = originalWidth;
    let height = originalHeight;

    if (maxWidth && width > maxWidth) {
      height = (height * maxWidth) / width;
      width = maxWidth;
    }

    if (maxHeight && height > maxHeight) {
      width = (width * maxHeight) / height;
      height = maxHeight;
    }

    return { width: Math.round(width), height: Math.round(height) };
  }

  // Batch process multiple images with memory management
  static async batchProcessImages(
    files: File[],
    processor: (file: File) => Promise<any>,
    batchSize: number = 2
  ): Promise<any[]> {
    const results: any[] = [];
    
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(file => processor(file))
      );
      results.push(...batchResults);
      
      // Force garbage collection between batches
      if (typeof window !== 'undefined' && 'gc' in window) {
        (window as any).gc();
      }
      
      // Small delay to prevent UI blocking
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    return results;
  }

  // Memory cleanup utility
  static cleanup() {
    // Clear canvas pool
    this.canvasPool.forEach(canvas => {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    });
    this.canvasPool = [];
    
    // Force garbage collection if available
    if (typeof window !== 'undefined' && 'gc' in window) {
      (window as any).gc();
    }
  }
}

// Web Worker for heavy image processing
export function createImageProcessingWorker(): Worker | null {
  if (typeof Worker === 'undefined') return null;

  const workerCode = `
    self.onmessage = function(e) {
      const { imageData, operation, params } = e.data;
      
      try {
        let result;
        
        switch (operation) {
          case 'compress':
            result = compressImageData(imageData, params);
            break;
          case 'filter':
            result = applyFilter(imageData, params);
            break;
          default:
            throw new Error('Unknown operation');
        }
        
        self.postMessage({ success: true, result });
      } catch (error) {
        self.postMessage({ success: false, error: error.message });
      }
    };
    
    function compressImageData(imageData, params) {
      // Implement compression logic here
      return imageData;
    }
    
    function applyFilter(imageData, params) {
      // Implement filter logic here
      return imageData;
    }
  `;

  const blob = new Blob([workerCode], { type: 'application/javascript' });
  return new Worker(URL.createObjectURL(blob));
}

// Performance monitoring for image operations
export class ImagePerformanceMonitor {
  private static operations: Map<string, number[]> = new Map();

  static startOperation(operationName: string): string {
    const operationId = `${operationName}_${Date.now()}_${Math.random()}`;
    const startTime = performance.now();
    
    if (!this.operations.has(operationName)) {
      this.operations.set(operationName, []);
    }
    
    this.operations.get(operationName)!.push(startTime);
    return operationId;
  }

  static endOperation(operationId: string) {
    const endTime = performance.now();
    const operationName = operationId.split('_')[0];
    const operations = this.operations.get(operationName);
    
    if (operations && operations.length > 0) {
      const startTime = operations.pop()!;
      const duration = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`Image operation ${operationName}: ${duration.toFixed(2)}ms`);
        
        if (duration > 1000) {
          console.warn(`Slow image operation detected: ${operationName} took ${duration.toFixed(2)}ms`);
        }
      }
    }
  }

  static getAverageTime(operationName: string): number {
    const operations = this.operations.get(operationName);
    if (!operations || operations.length === 0) return 0;
    
    const sum = operations.reduce((a, b) => a + b, 0);
    return sum / operations.length;
  }
}
