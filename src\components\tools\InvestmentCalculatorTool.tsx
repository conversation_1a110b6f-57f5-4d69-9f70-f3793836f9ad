
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  initialAmount: requiredNumber().nonnegative("المبلغ الأولي لا يمكن أن يكون سالبًا.").default(1000),
  monthlyContribution: requiredNumber().nonnegative("المساهمة الشهرية لا يمكن أن تكون سالبة.").default(100),
  annualReturn: requiredNumber().nonnegative("العائد السنوي لا يمكن أن يكون سالبًا.").default(7),
  years: requiredNumber().int().positive("مدة الاستثمار يجب أن تكون سنة واحدة على الأقل.").default(10),
  compoundingFrequency: z.enum(['annually', 'monthly']).default('annually'),
});

interface Result {
  futureValue: number;
  totalInvested: number;
  totalInterest: number;
}

export function InvestmentCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      initialAmount: 1000,
      monthlyContribution: 100,
      annualReturn: 7,
      years: 10,
      compoundingFrequency: 'annually',
    },
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { initialAmount, monthlyContribution, annualReturn, years, compoundingFrequency } = data;

    const rate = annualReturn / 100;
    const t = years;

    let fvInitial = 0;
    let fvContributions = 0;

    if (compoundingFrequency === 'monthly') {
      // التركيب الشهري
      const monthlyRate = rate / 12;
      const totalMonths = years * 12;

      // القيمة المستقبلية للاستثمار الأولي
      fvInitial = initialAmount * Math.pow(1 + monthlyRate, totalMonths);

      // القيمة المستقبلية للمساهمات الشهرية
      if (monthlyContribution > 0) {
        fvContributions = monthlyContribution * ((Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate);
      }
    } else {
      // التركيب السنوي
      // القيمة المستقبلية للاستثمار الأولي
      fvInitial = initialAmount * Math.pow(1 + rate, t);

      // القيمة المستقبلية للمساهمات الشهرية مع التركيب السنوي
      if (monthlyContribution > 0) {
        // حساب المساهمات السنوية (12 شهر × المساهمة الشهرية)
        const annualContribution = monthlyContribution * 12;

        // حساب القيمة المستقبلية للمساهمات السنوية
        for (let i = 0; i < t; i++) {
          // كل مساهمة سنوية تنمو لعدد السنوات المتبقية
          fvContributions += annualContribution * Math.pow(1 + rate, t - i - 1);
        }
      }
    }

    const futureValue = fvInitial + fvContributions;
    const totalInvested = initialAmount + (monthlyContribution * 12 * years);
    const totalInterest = futureValue - totalInvested;

    setResult({ futureValue, totalInvested, totalInterest });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة الاستثمار (الفائدة المركبة)</CardTitle>
        <CardDescription>قدّر نمو استثماراتك بمرور الوقت مع قوة الفائدة المركبة.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField name="initialAmount" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>المبلغ الأولي للاستثمار</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="monthlyContribution" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>المساهمة الشهرية</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="annualReturn" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>نسبة العائد السنوي (%)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="years" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>مدة الاستثمار (بالسنوات)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
            </div>
             <FormField control={form.control} name="compoundingFrequency" render={({ field }) => (
                <FormItem>
                  <FormLabel>تكرار حساب الفائدة</FormLabel>
                   <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl>
                      <SelectContent>
                        <SelectItem value="annually">سنويًا</SelectItem>
                        <SelectItem value="monthly">شهريًا</SelectItem>
                      </SelectContent>
                    </Select>
                  <FormMessage />
                </FormItem>
            )}/>
            <Button type="submit" className="w-full">احسب الاستثمار</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">قيمة استثمارك المستقبلية (تقديري)</h3>
            <div className="p-6 bg-primary/10 rounded-lg mb-4">
                <div className="overflow-hidden">
                  <p className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary break-words leading-tight">
                    {result.futureValue.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', minimumFractionDigits: 0, numberingSystem: 'latn' })}
                  </p>
                </div>
            </div>
             <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-secondary rounded-lg">
                    <p className="text-sm text-muted-foreground">إجمالي المبلغ المستثمر</p>
                    <div className="overflow-hidden">
                      <p className="text-xl md:text-2xl font-bold break-words leading-tight">{result.totalInvested.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', minimumFractionDigits: 0, numberingSystem: 'latn' })}</p>
                    </div>
                </div>
                <div className="p-4 bg-green-500/10 rounded-lg">
                    <p className="text-sm text-green-700">إجمالي الأرباح</p>
                    <div className="overflow-hidden">
                      <p className="text-xl md:text-2xl font-bold text-green-600 break-words leading-tight">{result.totalInterest.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', minimumFractionDigits: 0, numberingSystem: 'latn' })}</p>
                    </div>
                </div>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
              هذا الرقم هو تقدير بناءً على المدخلات. النتائج الفعلية قد تختلف.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
