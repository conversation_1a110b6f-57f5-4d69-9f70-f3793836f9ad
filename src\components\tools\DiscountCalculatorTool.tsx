'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  originalPrice: requiredNumber().positive({ message: 'يجب أن يكون السعر الأصلي رقمًا موجبًا' }),
  discountType: z.enum(['percentage', 'amount']),
  discountValue: requiredNumber().nonnegative({ message: 'يجب أن تكون قيمة الخصم رقمًا غير سالب' }),
});

interface Result {
  finalPrice: number;
  savedAmount: number;
  discountPercentage: number;
}

export function DiscountCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      originalPrice: 100,
      discountType: 'percentage',
      discountValue: 10,
    },
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { originalPrice, discountType, discountValue } = data;
    
    let finalPrice: number;
    let savedAmount: number;
    let discountPercentage: number;
    
    if (discountType === 'percentage') {
      // تأكد من أن النسبة المئوية لا تتجاوز 100%
      const safeDiscountValue = Math.min(discountValue, 100);
      finalPrice = originalPrice * (1 - safeDiscountValue / 100);
      savedAmount = originalPrice - finalPrice;
      discountPercentage = safeDiscountValue;
    } else {
      // تأكد من أن مبلغ الخصم لا يتجاوز السعر الأصلي
      const safeDiscountValue = Math.min(discountValue, originalPrice);
      finalPrice = originalPrice - safeDiscountValue;
      savedAmount = safeDiscountValue;
      discountPercentage = (savedAmount / originalPrice) * 100;
    }
    
    setResult({
      finalPrice,
      savedAmount,
      discountPercentage,
    });
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>حاسبة الخصم</CardTitle>
        <CardDescription>أدخل السعر الأصلي وقيمة الخصم لحساب السعر النهائي والمبلغ الموفر.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="originalPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>السعر الأصلي</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="discountType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>نوع الخصم</FormLabel>
                  <FormControl>
                    <Tabs 
                      defaultValue={field.value} 
                      className="w-full" 
                      onValueChange={field.onChange}
                    >
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="percentage">نسبة مئوية (%)</TabsTrigger>
                        <TabsTrigger value="amount">مبلغ</TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="discountValue"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {form.watch('discountType') === 'percentage' 
                      ? 'نسبة الخصم (%)' 
                      : 'مبلغ الخصم'}
                  </FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <Button type="submit" className="w-full">
              احسب
            </Button>
          </form>
        </Form>
        
        {result && (
          <div className="mt-8 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-primary/10 rounded-lg text-center">
                <div className="overflow-hidden">
                  <p className="text-2xl md:text-3xl font-bold text-primary break-words leading-tight">
                    {result.finalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </p>
                </div>
                <p className="text-sm text-muted-foreground">السعر بعد الخصم</p>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg text-center">
                <div className="overflow-hidden">
                  <p className="text-2xl md:text-3xl font-bold text-primary break-words leading-tight">
                    {result.savedAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </p>
                </div>
                <p className="text-sm text-muted-foreground">المبلغ الموفر</p>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg text-center">
                <div className="overflow-hidden">
                  <p className="text-2xl md:text-3xl font-bold text-primary break-words leading-tight">
                    {result.discountPercentage.toLocaleString('en-US', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}%
                  </p>
                </div>
                <p className="text-sm text-muted-foreground">نسبة الخصم</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
