
'use client';

import { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, Alert<PERSON><PERSON>gle, Shrink, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';
import { Label } from '../ui/label';
import { Slider } from '../ui/slider';
import { OptimizedImageProcessor, ImagePerformanceMonitor } from '@/utils/optimizedImageProcessing';

export function ImageCompressorTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [compressedImage, setCompressedImage] = useState<string | null>(null);
  const [originalSize, setOriginalSize] = useState<number>(0);
  const [compressedSize, setCompressedSize] = useState<number>(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [quality, setQuality] = useState(0.8); // 80% quality
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      OptimizedImageProcessor.cleanup();
    };
  }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!['image/jpeg', 'image/png'].includes(file.type)) {
      setError('يرجى اختيار ملف بصيغة JPG أو PNG.');
      return;
    }
    if (file.size > 20 * 1024 * 1024) { // 20MB limit
      setError('حجم الملف كبير جدًا. يرجى اختيار ملف أصغر من 20 ميجابايت.');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setOriginalImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    
    setSelectedFile(file);
    setOriginalSize(file.size);
    setError(null);
    setCompressedImage(null);
    setCompressedSize(0);
    toast({ title: 'تم اختيار الملف', description: `تم اختيار ${file.name}.` });
  };

  const compressImage = async () => {
    if (!selectedFile || !originalImage) return;

    setIsProcessing(true);
    setError(null);
    setCompressedImage(null);
    setCompressedSize(0);

    const operationId = ImagePerformanceMonitor.startOperation('compress');

    try {
      const result = await OptimizedImageProcessor.compressImage(
        selectedFile,
        quality,
        2048, // Max width
        2048  // Max height
      );

      setCompressedImage(result.dataUrl);
      setCompressedSize(result.size);
      toast({
        title: 'تم ضغط الصورة بنجاح!',
        description: `تم تقليل الحجم بنسبة ${Math.round((1 - result.size / originalSize) * 100)}%`
      });

    } catch (error) {
      setError('فشل في ضغط الصورة. يرجى المحاولة مرة أخرى.');
      console.error('Compression error:', error);
    } finally {
      setIsProcessing(false);
      ImagePerformanceMonitor.endOperation(operationId);
    }
  };

  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  const clearFile = () => {
    setSelectedFile(null);
    setError(null);
    setOriginalImage(null);
    setCompressedImage(null);
    setOriginalSize(0);
    setCompressedSize(0);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>ضاغط الصور</CardTitle>
        <CardDescription>قلل حجم ملفات الصور (JPG/PNG) مع التحكم في مستوى الجودة.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {error && <Alert variant="destructive"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}

        {!selectedFile && (
          <div className="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer" onClick={() => fileInputRef.current?.click()}>
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-primary/10 rounded-full"><Upload className="h-8 w-8 text-primary" /></div>
              <div>
                <h3 className="text-lg font-semibold mb-2">اختر صورة للضغط</h3>
                <p className="text-gray-600 mb-4">اسحب وأفلت الصورة هنا أو انقر للاختيار</p>
                <Button>اختر صورة</Button>
              </div>
              <p className="text-sm text-gray-500">الحد الأقصى: 20 ميجابايت</p>
            </div>
            <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/jpeg,image/png" className="hidden" />
          </div>
        )}
        
        {selectedFile && (
          <div className="space-y-4">
            <div className="text-center">
              <img src={originalImage!} alt="Original preview" className="max-w-full max-h-60 mx-auto rounded-md border" />
              <p className="text-sm text-muted-foreground mt-2">{selectedFile.name} ({formatBytes(originalSize)})</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="quality-slider">الجودة ({(quality * 100).toFixed(0)}%)</Label>
              <Slider
                id="quality-slider"
                defaultValue={[quality * 100]}
                max={100}
                step={1}
                onValueChange={(value) => setQuality(value[0] / 100)}
              />
            </div>
            
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={compressImage} disabled={isProcessing} className="flex-1">
                {isProcessing ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Shrink className="ml-2 h-4 w-4" />}
                {isProcessing ? 'جاري الضغط...' : 'ضغط الصورة'}
              </Button>
              <Button onClick={clearFile} variant="outline" disabled={isProcessing} className="flex-1"><Trash2 className="ml-2 h-4 w-4" /> تغيير الصورة</Button>
            </div>
          </div>
        )}
        
        {compressedImage && (
          <div className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
                <div className="text-center">
                    <h3 className="font-semibold mb-2">الصورة الأصلية</h3>
                    <img src={originalImage!} alt="Original" className="max-w-full rounded-md border" />
                    <p className="text-sm mt-1">{formatBytes(originalSize)}</p>
                </div>
                 <div className="text-center">
                    <h3 className="font-semibold mb-2">الصورة المضغوطة</h3>
                    <img src={compressedImage} alt="Compressed" className="max-w-full rounded-md border" />
                    <p className="text-sm mt-1 text-green-600 font-bold">{formatBytes(compressedSize)}</p>
                </div>
            </div>
             <p className="text-center text-lg mt-4 font-bold text-primary">
              تم تقليل الحجم بنسبة {Math.round(((originalSize - compressedSize) / originalSize) * 100)}%
            </p>
            <Button onClick={() => saveAs(compressedImage, `compressed_${selectedFile?.name}`)} className="w-full mt-4">
              <Download className="ml-2 h-4 w-4" />
              تحميل الصورة المضغوطة
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
