'use client';

import { useEffect } from 'react';

export function ServiceWorkerManager() {
  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      registerServiceWorker();
    }
  }, []);

  return null;
}

async function registerServiceWorker() {
  try {
    // Register service worker
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
      updateViaCache: 'none'
    });

    console.log('Service Worker registered successfully:', registration);

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New service worker is available
            showUpdateNotification();
          }
        });
      }
    });

    // Check for updates periodically
    setInterval(() => {
      registration.update();
    }, 60000); // Check every minute

    // Handle messages from service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'CACHE_UPDATED') {
        console.log('Cache updated:', event.data.url);
      }
    });

    // Preload critical resources
    preloadCriticalResources();

  } catch (error) {
    console.error('Service Worker registration failed:', error);
  }
}

function showUpdateNotification() {
  // Create update notification
  const notification = document.createElement('div');
  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4f46e5;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 10000;
      font-family: 'Tajawal', sans-serif;
      direction: rtl;
      max-width: 300px;
    ">
      <div style="font-weight: 600; margin-bottom: 8px;">
        تحديث متاح
      </div>
      <div style="font-size: 14px; margin-bottom: 12px; opacity: 0.9;">
        يتوفر إصدار جديد من الموقع
      </div>
      <button onclick="updateServiceWorker()" style="
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-family: inherit;
        margin-left: 8px;
      ">
        تحديث الآن
      </button>
      <button onclick="this.parentElement.parentElement.remove()" style="
        background: transparent;
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-family: inherit;
      ">
        لاحقاً
      </button>
    </div>
  `;

  document.body.appendChild(notification);

  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 10000);
}

// Global function for update button
if (typeof window !== 'undefined') {
  (window as any).updateServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration && registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        window.location.reload();
      }
    }
  };
}

function preloadCriticalResources() {
  if (!('serviceWorker' in navigator)) return;

  const criticalResources = [
    '/',
    '/tools',
    '/about',
    '/_next/static/css/',
    '/_next/static/js/'
  ];

  navigator.serviceWorker.ready.then((registration) => {
    if (registration.active) {
      registration.active.postMessage({
        type: 'CACHE_URLS',
        urls: criticalResources
      });
    }
  });
}

// Network status monitoring
export function useNetworkStatus() {
  useEffect(() => {
    const handleOnline = () => {
      console.log('Network: Online');
      // Sync any pending offline actions
      syncOfflineActions();
    };

    const handleOffline = () => {
      console.log('Network: Offline');
      // Show offline indicator
      showOfflineIndicator();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
}

function showOfflineIndicator() {
  const indicator = document.createElement('div');
  indicator.id = 'offline-indicator';
  indicator.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #ef4444;
      color: white;
      text-align: center;
      padding: 8px;
      font-family: 'Tajawal', sans-serif;
      z-index: 10001;
      font-size: 14px;
    ">
      📡 غير متصل بالإنترنت - بعض الميزات قد لا تعمل
    </div>
  `;

  document.body.appendChild(indicator);

  // Remove when back online
  const removeIndicator = () => {
    const element = document.getElementById('offline-indicator');
    if (element) {
      element.remove();
    }
    window.removeEventListener('online', removeIndicator);
  };

  window.addEventListener('online', removeIndicator);
}

async function syncOfflineActions() {
  // Sync any queued offline actions
  if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
    const registration = await navigator.serviceWorker.ready;
    await registration.sync.register('background-sync');
  }
}

// Cache management utilities
export const CacheManager = {
  // Clear all caches
  async clearAll() {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
      console.log('All caches cleared');
    }
  },

  // Get cache size
  async getSize() {
    if ('caches' in window && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        quota: estimate.quota || 0,
        percentage: estimate.usage && estimate.quota ? 
          Math.round((estimate.usage / estimate.quota) * 100) : 0
      };
    }
    return { used: 0, quota: 0, percentage: 0 };
  },

  // Preload specific URLs
  async preload(urls: string[]) {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.ready;
      if (registration.active) {
        registration.active.postMessage({
          type: 'CACHE_URLS',
          urls
        });
      }
    }
  }
};

// Performance monitoring
export function monitorCachePerformance() {
  if (typeof window === 'undefined' || !('performance' in window)) return;

  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      const resource = entry as PerformanceResourceTiming;
      
      // Log cache hits vs misses
      if (resource.transferSize === 0 && resource.decodedBodySize > 0) {
        console.log('Cache hit:', resource.name);
      } else if (resource.transferSize > 0) {
        console.log('Network request:', resource.name, `${Math.round(resource.transferSize / 1024)}KB`);
      }
    }
  });

  observer.observe({ entryTypes: ['resource'] });
}
