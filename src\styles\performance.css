/* Performance-optimized CSS */

/* Critical rendering optimizations */
.critical-section {
  contain: layout style;
  will-change: auto;
}

.hero-section {
  contain: layout style;
  content-visibility: auto;
  contain-intrinsic-size: 0 600px;
}

/* Lazy loading optimizations */
.lazy-section {
  content-visibility: auto;
  contain-intrinsic-size: 0 400px;
}

.tool-grid {
  contain: layout;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.tool-card {
  contain: layout style;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.tool-card:hover {
  transform: translateY(-2px) translateZ(0);
}

/* Image optimizations */
.optimized-image {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
  object-fit: cover;
  loading: lazy;
  decoding: async;
}

/* Animation optimizations */
.smooth-animation {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
  animation-fill-mode: both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .smooth-animation,
  .fade-in {
    animation: none;
    transition: none;
    will-change: auto;
  }
}

/* Font loading optimizations */
.font-loading {
  font-display: swap;
  font-feature-settings: "liga" 1, "kern" 1;
}

.font-loaded .font-loading {
  font-family: 'Tajawal-Optimized', system-ui, sans-serif;
}

/* Layout containment for better performance */
.sidebar {
  contain: layout style;
}

.main-content {
  contain: layout;
}

.footer {
  contain: layout style;
}

/* Scroll optimizations */
.scroll-container {
  overflow-y: auto;
  overscroll-behavior: contain;
  scroll-behavior: smooth;
}

/* GPU acceleration for heavy elements */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* Memory-efficient shadows */
.efficient-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Optimized gradients */
.efficient-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
}

/* Print optimizations */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-optimized {
    color: black !important;
    background: white !important;
    box-shadow: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tool-card {
    border: 2px solid;
  }
  
  .button {
    border: 2px solid;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    background-color: #1a1a1a;
    color: #ffffff;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .mobile-optimized {
    contain: layout;
    transform: translateZ(0);
  }
  
  .tool-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .hero-section {
    contain-intrinsic-size: 0 400px;
  }
}

/* Touch optimizations */
@media (hover: none) and (pointer: coarse) {
  .touch-optimized {
    min-height: 44px;
    min-width: 44px;
  }
  
  .tool-card:hover {
    transform: none;
  }
}

/* Prefers reduced data */
@media (prefers-reduced-data: reduce) {
  .data-heavy {
    display: none;
  }
  
  .optimized-image {
    content-visibility: hidden;
  }
}

/* Focus optimizations */
.focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Error boundaries */
.error-boundary {
  contain: layout style;
  isolation: isolate;
}

/* Performance monitoring classes */
.perf-critical {
  /* Mark critical performance sections */
  contain: strict;
}

.perf-lazy {
  /* Mark sections that can be lazy loaded */
  content-visibility: auto;
}

/* Utility classes for performance */
.contain-layout { contain: layout; }
.contain-style { contain: style; }
.contain-paint { contain: paint; }
.contain-size { contain: size; }
.contain-strict { contain: strict; }

.will-change-transform { will-change: transform; }
.will-change-opacity { will-change: opacity; }
.will-change-auto { will-change: auto; }

.gpu-layer { transform: translateZ(0); }
.backface-hidden { backface-visibility: hidden; }

/* Critical path CSS inlining marker */
.critical-css {
  /* This class marks critical CSS that should be inlined */
}
