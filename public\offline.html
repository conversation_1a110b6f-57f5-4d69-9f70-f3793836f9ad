<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - جامع الأدوات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            direction: rtl;
        }
        
        .offline-container {
            max-width: 500px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }
        
        .offline-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        
        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .offline-features {
            margin-top: 2rem;
            text-align: right;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .feature-icon {
            margin-left: 0.5rem;
            font-size: 1.2rem;
        }
        
        @media (max-width: 768px) {
            .offline-container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
        }
        
        .loading-animation {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1 class="offline-title">غير متصل بالإنترنت</h1>
        <p class="offline-message">
            يبدو أنك غير متصل بالإنترنت حالياً. تحقق من اتصالك وحاول مرة أخرى.
        </p>
        
        <button class="retry-button" onclick="retryConnection()">
            <span class="loading-animation hidden" id="loading"></span>
            <span id="button-text">إعادة المحاولة</span>
        </button>
        
        <div class="offline-features">
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>بعض الأدوات متاحة دون اتصال</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>سيتم حفظ عملك تلقائياً</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>سيتم المزامنة عند العودة للاتصال</span>
            </div>
        </div>
    </div>

    <script>
        function retryConnection() {
            const button = document.querySelector('.retry-button');
            const loading = document.getElementById('loading');
            const buttonText = document.getElementById('button-text');
            
            // Show loading state
            loading.classList.remove('hidden');
            buttonText.textContent = 'جاري المحاولة...';
            button.disabled = true;
            
            // Check connection
            fetch('/', { method: 'HEAD', cache: 'no-cache' })
                .then(response => {
                    if (response.ok) {
                        // Connection restored
                        buttonText.textContent = 'تم الاتصال!';
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        throw new Error('No connection');
                    }
                })
                .catch(() => {
                    // Still offline
                    loading.classList.add('hidden');
                    buttonText.textContent = 'إعادة المحاولة';
                    button.disabled = false;
                    
                    // Show temporary message
                    const originalMessage = buttonText.textContent;
                    buttonText.textContent = 'لا يزال غير متصل';
                    setTimeout(() => {
                        buttonText.textContent = originalMessage;
                    }, 2000);
                });
        }
        
        // Auto-retry when online
        window.addEventListener('online', () => {
            document.querySelector('.offline-title').textContent = 'تم استعادة الاتصال!';
            document.querySelector('.offline-message').textContent = 'جاري إعادة تحميل الصفحة...';
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        });
        
        // Check connection status periodically
        setInterval(() => {
            if (navigator.onLine) {
                fetch('/', { method: 'HEAD', cache: 'no-cache' })
                    .then(response => {
                        if (response.ok) {
                            window.location.reload();
                        }
                    })
                    .catch(() => {
                        // Still offline
                    });
            }
        }, 30000); // Check every 30 seconds
    </script>
</body>
</html>
