
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '../ui/label';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  amount: requiredNumber().min(0.01, { message: 'الرجاء إدخال مبلغ صحيح.' }),
  rate: requiredNumber().min(0, { message: 'الرجاء إدخال نسبة ضريبة صحيحة.' }).default(15),
  isVatIncluded: z.boolean().default(false),
});

interface Result {
  total: string;
  taxAmount: string;
  basePrice: string;
}

export function VatCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      rate: 15,
      isVatIncluded: false,
    },
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { amount, rate, isVatIncluded } = data;
    const taxMultiplier = rate / 100;
    
    let total: number;
    let taxAmount: number;
    let basePrice: number;

    if (isVatIncluded) {
      // استخراج الضريبة من المبلغ الإجمالي
      total = amount;
      basePrice = amount / (1 + taxMultiplier);
      taxAmount = total - basePrice;
    } else {
      // إضافة الضريبة إلى المبلغ الأساسي
      basePrice = amount;
      taxAmount = basePrice * taxMultiplier;
      total = basePrice + taxAmount;
    }
    
    setResult({
      total: total.toFixed(2),
      taxAmount: taxAmount.toFixed(2),
      basePrice: basePrice.toFixed(2),
    });
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>حاسبة ضريبة القيمة المضافة</CardTitle>
        <CardDescription>احسب ضريبة القيمة المضافة بسهولة، سواء بإضافتها للسعر أو استخراجها منه.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            
            <FormField
              control={form.control}
              name="isVatIncluded"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>هل السعر شامل لضريبة القيمة المضافة؟</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => field.onChange(value === 'true')}
                      defaultValue={String(field.value)}
                      className="flex gap-x-6 justify-end"
                    >
                      <FormItem className="flex items-center gap-2 flex-row-reverse">
                        <Label htmlFor="r1">نعم</Label>
                        <FormControl>
                          <RadioGroupItem value="true" id="r1" />
                        </FormControl>
                      </FormItem>
                      <FormItem className="flex items-center gap-2 flex-row-reverse">
                        <Label htmlFor="r2">لا</Label>
                        <FormControl>
                          <RadioGroupItem value="false" id="r2" />
                        </FormControl>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
               <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>المبلغ</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="مثال: 100" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>نسبة الضريبة (%)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="15" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
            </div>
           
            <Button type="submit" className="w-full">
              احسب
            </Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8">
            <h3 className="text-xl font-headline font-semibold mb-4 text-center">النتائج</h3>
            <Card className="bg-muted/50">
              <CardContent className="p-0">
                <Table>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">السعر الأساسي (بدون ضريبة)</TableCell>
                      <TableCell className="text-left text-lg break-words">{result.basePrice}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">قيمة الضريبة</TableCell>
                      <TableCell className="text-left text-lg break-words">{result.taxAmount}</TableCell>
                    </TableRow>
                    <TableRow className="bg-primary/10">
                      <TableCell className="font-bold text-primary">الإجمالي (شامل الضريبة)</TableCell>
                      <TableCell className="text-left text-xl font-bold text-primary break-words">{result.total}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
