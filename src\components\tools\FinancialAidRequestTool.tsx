
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Copy, FileText, RefreshCw, FileDown, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';
import { saveAs } from 'file-saver';
// import { generateDocx } from '@/lib/actions/docx'; // Removed - library not working
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

const FormSchema = z.object({
    senderName: z.string().min(1, 'اسم المرسل مطلوب.'),
    senderId: z.string().optional(),
    senderAddress: z.string().optional(),
    senderPhone: z.string().optional(),
    senderEmail: z.string().email({ message: "الرجاء إدخال بريد إلكتروني صالح." }).optional().or(z.literal('')),
    
    recipientName: z.string().optional(),
    recipientTitle: z.string().optional(),
    recipientGender: z.enum(['male', 'female']).default('male'),

    reason: z.string().min(1, 'الرجاء شرح سبب الحاجة للمساعدة.'),
    amount: z.string().optional(),
    documents: z.string().optional(),
    
    template: z.enum([
        'general_aid', 
        'mbs_aid', 
        'philanthropist_aid', 
        'alwaleed_aid', 
        'royal_court_aid'
    ]).default('general_aid'),
    dateType: z.enum(['gregorian', 'hijri', 'none']).default('gregorian'),
});

type FormValues = z.infer<typeof FormSchema>;

const getDateString = (dateType: 'gregorian' | 'hijri' | 'none') => {
    const today = new Date();
    switch (dateType) {
        case 'gregorian':
             return new Intl.DateTimeFormat('ar-SA-u-ca-gregory-nu-latn', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
            }).format(today);
        case 'hijri':
            return new Intl.DateTimeFormat('ar-SA-u-ca-islamic-nu-latn', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            }).format(today);
        case 'none':
            return '../../....';
        default:
            return '';
    }
};

const templates: { [key: string]: { label: string, generator: (data: FormValues) => string } } = {
  general_aid: {
    label: "صيغة معروض طلب مساعدة مالية جاهز",
    generator: (data) => {
        const title = data.recipientGender === 'male' ? 'سيدي' : 'سيدتي';
        const pronoun = data.recipientGender === 'male' ? 'سموكم' : 'سموكن';
        return `
إلى ${title} صاحب السمو الملكي الأمير ${data.recipientName || '.........................'}

الموضوع: طلب مساعدة مالية

أتقدم إليكم بهذا الطلب راجياً من ${pronoun} النظر بعين العطف والرحمة في حالتي المادية الصعبة، حيث أعاني من ${data.reason}.

أُرفق مع هذا الطلب المستندات التالية:
${data.documents || 'قائمة بالمستندات المرفقة'}

أرجو من ${pronoun} التكرم بالنظر في طلبي وتقديم المساعدة المالية لي، لكي أتمكن من تحسين ظروفي.

أشكر لكم حسن العناية والاهتمام، وأدعو الله أن يحفظكم ويرعاكم.

مع خالص الشكر والتقدير،

اسمك الكامل: ${data.senderName}
التوقيع: ...........................
تاريخ اليوم: ${getDateString(data.dateType)}
`.trim()
    },
  },
  mbs_aid: {
    label: "طلب مساعدة مالية من محمد بن سلمان",
    generator: (data) => `
التاريخ: ${getDateString(data.dateType)}
اسم المرسل: ${data.senderName}
رقم الهوية الوطنية: ${data.senderId || '.........................'}
العنوان: ${data.senderAddress || '.........................'}
رقم الهاتف: ${data.senderPhone || '.........................'}
البريد الإلكتروني: ${data.senderEmail || '.........................'}

إلى صاحب السمو الملكي الأمير محمد بن سلمان بن عبد العزيز آل سعود
ولي العهد، نائب رئيس مجلس الوزراء، وزير الدفاع
حفظه الله ورعاه

السلام عليكم ورحمة الله وبركاته،

أتقدم إلى سموكم الكريم بهذا الطلب راجيًا من الله ثم منكم مد يد العون والمساعدة المالية، حيث أواجه ظروفًا مالية صعبة تعيق قدرتي على تلبية احتياجاتي الأساسية وتوفير حياة كريمة لعائلتي.
${data.reason}

لقد عُرف عن سموكم الكريم حبكم لفعل الخير ومساعدة المحتاجين، لذلك أرجو أن تشملني عنايتكم الكريمة وتقديركم لحالتي. أرفق مع هذا الطلب جميع الوثائق التي توضح وضعي المالي الحالي وتؤكد حاجتي للمساعدة.

أسأل الله أن يوفقكم ويحفظكم، ويجزيكم خير الجزاء على ما تقدمه من دعم وعون.

تفضلوا بقبول خالص الشكر والتقدير.

اسمك الكامل: ${data.senderName}
توقيعك: .........................
`.trim(),
  },
  philanthropist_aid: {
    label: "نموذج معروض طلب مساعدة مالية من فاعل خير",
    generator: (data) => `
بسم الله الرحمن الرحيم

السلام عليكم ورحمة الله وبركاته،

تحية طيبة وبعد،

أنا ${data.senderName}، أكتب لكم هذا المعروض طالبًا مساعدتكم المالية بسبب ${data.reason}.

أحتاج إلى مبلغ ${data.amount || '.........'} لتغطية المصاريف. هذه المساعدة ستساهم بشكل كبير في تحسين وضعي المالي وتخفيف العبء الكبير الذي أواجه.

أشكركم على وقتكم واهتمامكم، وأرجو من الله أن يجعل مساعدتكم في ميزان حسناتكم.

يمكنكم التواصل معي عبر رقم الهاتف ${data.senderPhone || '.........................'}.

وفي الختام، أسأل الله العظيم أن يوفقكم ويسدد خطاكم لما فيه الخير، وأن يجعل هذا العمل في ميزان حسناتكم.

اسمك الكامل: ${data.senderName}
التوقيع: .........................
`.trim(),
  },
  alwaleed_aid: {
    label: "صيغة معروض طلب مساعدة مالية من الأمير الوليد بن طلال",
    generator: (data) => `
إلى صاحب السمو الملكي الأمير الوليد بن طلال آل سعود حفظه الله

الموضوع: طلب مساعدة مالية

السلام عليكم ورحمة الله وبركاته،

أتقدم إليكم أنا المواطن ${data.senderName} بخالص التحية والتقدير، وأرفع إلى سموكم هذا الطلب راجيًا من الله ثم من كرمكم وعطفكم المعروف عنكم أن تنظروا في ظروفي المعيشية الصعبة التي أمر بها، حيث أواجه ${data.reason}.

لقد سمعت عن مبادراتكم الخيرية التي دائمًا ما تمد يد العون للمحتاجين، وهذا ما شجعني على التوجه إليكم. أرفق مع طلبي المستندات التي توضح حالتي (${data.documents || 'كشف حساب، تقارير طبية، إلخ'})، وأتمنى أن تنال طلبي هذا عنايتكم الكريمة.

أملًا من الله ثم من سموكم الكريم مساعدتي في تخفيف معاناتي والوقوف بجانبي في هذه الظروف.

أدعو الله أن يديم عليكم الصحة والعافية، وأن يجعل ما تقدمونه من أعمال خير في ميزان حسناتكم.

مع خالص الشكر والتقدير،

الاسم الكامل: ${data.senderName}
رقم الهوية: ${data.senderId || '.........................'}
رقم الجوال: ${data.senderPhone || '.........................'}
العنوان: ${data.senderAddress || '.........................'}
التوقيع: .........................
`.trim(),
  },
  royal_court_aid: {
    label: "نموذج طلب مساعدة مالية من الديوان الملكي السعودي",
    generator: (data) => `
إلى الديوان الملكي السعودي،

السلام عليكم ورحمة الله وبركاته،

أنا ${data.senderName}، أعيش في ${data.senderAddress || '[مكان الإقامة]'}، أتقدم إليكم بهذا الطلب راجيًا المساعدة في ظل الظروف المالية التي أمر بها. أحتاج إلى مبلغ مالي يساعدني في ${data.reason}.

أرفقت المستندات اللازمة التي تثبت وضعي المالي وأملي كبير في كرمكم واهتمامكم بحالات المحتاجين.

أشكركم على وقتكم وأرجو من الله أن يوفقكم دائمًا في عمل الخير.

اسم المرسل: ${data.senderName}
التوقيع: .........................
`.trim(),
  },
};

const TemplateFields = ({ template, control }: { template: string, control: any }) => {
    switch(template) {
        case 'general_aid':
        case 'alwaleed_aid':
            return (
                <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField control={control} name="recipientName" render={({ field }) => (<FormItem><FormLabel>اسم المستلم / الجهة</FormLabel><FormControl><Input placeholder="مثال: الأمير خالد الفيصل" {...field} /></FormControl><FormMessage /></FormItem>)} />
                        <FormField control={control} name="recipientTitle" render={({ field }) => (<FormItem><FormLabel>المسمى الوظيفي (اختياري)</FormLabel><FormControl><Input placeholder="مثال: أمير منطقة مكة" {...field} /></FormControl><FormMessage /></FormItem>)} />
                    </div>
                     <FormField control={control} name="recipientGender" render={({ field }) => (
                      <FormItem className="space-y-3"><FormLabel>جنس المستلم</FormLabel>
                        <FormControl>
                          <RadioGroup onValueChange={field.onChange} defaultValue={String(field.value)} className="flex gap-4 pt-2 " dir="rtl">
                            <div className="flex items-center gap-2 flex-row-reverse"><Label className="font-normal">ذكر</Label><RadioGroupItem value="male" /></div>
                            <div className="flex items-center gap-2 flex-row-reverse"><Label className="font-normal">أنثى</Label><RadioGroupItem value="female" /></div>
                          </RadioGroup>
                        </FormControl>
                      </FormItem>
                    )} />
                    <FormField control={control} name="reason" render={({ field }) => (<FormItem><FormLabel>سبب طلب المساعدة</FormLabel><FormControl><Textarea placeholder="اشرح ظروفك بإيجاز..." {...field} /></FormControl><FormMessage /></FormItem>)} />
                    <FormField control={control} name="documents" render={({ field }) => (<FormItem><FormLabel>المستندات المرفقة (اختياري)</FormLabel><FormControl><Input placeholder="مثال: تقارير طبية، كشف حساب" {...field} /></FormControl><FormMessage /></FormItem>)} />
                </>
            );
        case 'mbs_aid':
             return (
                <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField control={control} name="senderId" render={({ field }) => (<FormItem><FormLabel>رقم الهوية</FormLabel><FormControl><Input placeholder="رقم الهوية" {...field} /></FormControl><FormMessage /></FormItem>)} />
                        <FormField control={control} name="senderPhone" render={({ field }) => (<FormItem><FormLabel>رقم الهاتف</FormLabel><FormControl><Input placeholder="رقم الهاتف" {...field} /></FormControl><FormMessage /></FormItem>)} />
                    </div>
                     <FormField control={control} name="senderAddress" render={({ field }) => (<FormItem><FormLabel>العنوان</FormLabel><FormControl><Input placeholder="المدينة، الحي" {...field} /></FormControl><FormMessage /></FormItem>)} />
                    <FormField control={control} name="senderEmail" render={({ field }) => (<FormItem><FormLabel>البريد الإلكتروني (اختياري)</FormLabel><FormControl><Input placeholder="<EMAIL>" {...field} /></FormControl><FormMessage /></FormItem>)} />
                    <FormField control={control} name="reason" render={({ field }) => (<FormItem><FormLabel>سبب طلب المساعدة</FormLabel><FormControl><Textarea placeholder="اشرح ظروفك المالية الصعبة..." {...field} /></FormControl><FormMessage /></FormItem>)} />
                </>
            );
        case 'philanthropist_aid':
            return (
                <>
                    <FormField control={control} name="reason" render={({ field }) => (<FormItem><FormLabel>سبب طلب المساعدة</FormLabel><FormControl><Textarea placeholder="اشرح ظروفك بإيجاز..." {...field} /></FormControl><FormMessage /></FormItem>)} />
                    <FormField control={control} name="amount" render={({ field }) => (<FormItem><FormLabel>المبلغ المطلوب (اختياري)</FormLabel><FormControl><Input placeholder="مثال: 5000 ريال" {...field} /></FormControl><FormMessage /></FormItem>)} />
                    <FormField control={control} name="senderPhone" render={({ field }) => (<FormItem><FormLabel>رقم الهاتف للتواصل</FormLabel><FormControl><Input placeholder="رقم الهاتف" {...field} /></FormControl><FormMessage /></FormItem>)} />
                </>
            );
        case 'royal_court_aid':
            return (
                <>
                    <FormField control={control} name="senderAddress" render={({ field }) => (<FormItem><FormLabel>مكان الإقامة</FormLabel><FormControl><Input placeholder="المدينة، الحي" {...field} /></FormControl><FormMessage /></FormItem>)} />
                    <FormField control={control} name="reason" render={({ field }) => (<FormItem><FormLabel>سبب طلب المساعدة</FormLabel><FormControl><Textarea placeholder="اشرح حاجتك للمساعدة بإيجاز..." {...field} /></FormControl><FormMessage /></FormItem>)} />
                </>
            );
        default:
            return null;
    }
}


export function FinancialAidRequestTool() {
  const [result, setResult] = useState<string>('');
  const [isDownloading, setIsDownloading] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      template: 'general_aid',
      dateType: 'gregorian',
      recipientGender: 'male',
    },
  });

  const { watch } = form;
  const selectedTemplate = watch('template');

  const onSubmit = (data: FormValues) => {
    const generator = templates[data.template].generator;
    const letter = generator(data);
    setResult(letter);
  };

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(result).then(() => {
        toast({
          title: 'تم النسخ بنجاح!',
          description: 'تم نسخ نص الطلب إلى الحافظة.',
        });
      });
    }
  };

  const handleDownload = async () => {
    if (!result) return;
    setIsDownloading(true);
    try {
        // Create a simple text file instead of DOCX
        const blob = new Blob([result], { type: 'text/plain;charset=utf-8' });
        saveAs(blob, 'طلب_مساعدة_مالية.txt');
        toast({
          title: 'تم التحميل بنجاح!',
          description: 'تم تحميل الطلب كملف نصي.',
        });
    } catch (error) {
        console.error('Error generating file:', error);
        toast({
          variant: 'destructive',
          title: 'فشل التحميل',
          description: 'حدث خطأ أثناء إنشاء الملف.',
        });
    } finally {
        setIsDownloading(false);
    }
  };


  const resetForm = () => {
    form.reset({
      senderName: '',
      senderId: '',
      senderAddress: '',
      senderPhone: '',
      senderEmail: '',
      recipientName: '',
      recipientTitle: '',
      reason: '',
      amount: '',
      documents: '',
      template: 'general_aid',
      dateType: 'gregorian',
    });
    setResult('');
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>بيانات الطلب</span>
            <Button variant="ghost" size="sm" onClick={resetForm}>
              <RefreshCw className="ml-2 h-4 w-4" />
              مسح الكل
            </Button>
          </CardTitle>
          <CardDescription>
            اختر القالب ثم املأ الحقول أدناه لإنشاء طلب مساعدة احترافي.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField control={form.control} name="template" render={({ field }) => (
                    <FormItem>
                        <FormLabel>اختر قالب الطلب</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                            <SelectTrigger><SelectValue placeholder="اختر نوع القالب" /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                            {Object.entries(templates).map(([key, value]) => (
                                <SelectItem key={key} value={key}>{value.label}</SelectItem>
                            ))}
                        </SelectContent>
                        </Select>
                        <FormMessage />
                    </FormItem>
                )} />

                <h4 className="font-bold pt-4 border-t">معلوماتك الشخصية (المرسل)</h4>
                <FormField control={form.control} name="senderName" render={({ field }) => (<FormItem><FormLabel>اسمك الكامل</FormLabel><FormControl><Input placeholder="اسمك" {...field} /></FormControl><FormMessage /></FormItem>)} />
                
                <div className="space-y-4 pt-4 border-t">
                  <h4 className="font-bold">تفاصيل الطلب (حسب القالب)</h4>
                  <TemplateFields template={selectedTemplate} control={form.control} />
                </div>
                
                <h4 className="font-bold pt-4 border-t">إعدادات الخطاب</h4>
                <FormField control={form.control} name="dateType" render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>تنسيق تاريخ اليوم</FormLabel>
                    <FormControl>
                      <RadioGroup onValueChange={field.onChange} defaultValue={String(field.value)} className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <Label className="border rounded-md p-3 flex items-center gap-2 cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary flex-row-reverse ">
                          <RadioGroupItem value="gregorian" /><span>تاريخ ميلادي</span>
                        </Label>
                        <Label className="border rounded-md p-3 flex items-center gap-2 cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary flex-row-reverse">
                          <RadioGroupItem value="hijri" /><span>تاريخ هجري</span>
                        </Label>
                         <Label className="border rounded-md p-3 flex items-center gap-2 cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary flex-row-reverse">
                          <RadioGroupItem value="none" /><span>بدون تاريخ</span>
                        </Label>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full">
                <FileText className="ml-2 h-4 w-4" />
                إنشاء الطلب
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      
      <Card className="bg-muted/30 flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>الطلب النهائي</span>
          </CardTitle>
          <CardDescription>هذا هو طلب المساعدة الجاهز للنسخ أو التحميل.</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col flex-1">
          <Textarea
            readOnly
            value={result || 'سيظهر الطلب النهائي هنا بعد ملء البيانات...'}
            className="min-h-[400px] whitespace-pre-wrap leading-loose bg-background flex-1"
          />
           {result && 
              <div className="flex flex-col sm:flex-row gap-2 mt-4">
                 <Button variant="outline" className="flex-1" onClick={handleDownload} disabled={isDownloading}>
                    {isDownloading ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <FileDown className="ml-2 h-4 w-4" />}
                     {isDownloading ? 'جاري التحميل...' : 'تحميل ملف نصي'}
                 </Button>
                 <Button variant="outline" className="flex-1" onClick={copyToClipboard}><Copy className="ml-2 h-4 w-4" /> نسخ النص</Button>
              </div>
            }
        </CardContent>
      </Card>
    </div>
  );
}
